import { useState, useCallback } from 'react';
import { Document, DocumentsByProduct, DocumentStatus } from '@/types';

interface UseDocumentValidationReturn {
  documents: DocumentsByProduct;
  loading: boolean;
  error: string | null;
  downloadDocument: (document: Document) => Promise<void>;
  replaceDocument: (documentId: string, newFile: File) => Promise<void>;
  deleteDocument: (documentId: string) => void;
  getProgressStats: () => {
    total: number;
    approved: number;
    rejected: number;
    pending: number;
  };
}

export const useDocumentValidation = (): UseDocumentValidationReturn => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Mock data - en producción esto vendría de una API
  const [documents, setDocuments] = useState<DocumentsByProduct>(() => {
    const mockDocuments: DocumentsByProduct = {
      'Wiki Gadget': {
        generales: [
          {
            id: '1',
            producto: 'Wiki Gadget',
            categoria: 'Generales',
            documento: 'Identificación Oficial',
            estado: 'Aprobado' as DocumentStatus,
            fechaCarga: '2024-01-15',
            url: '/documents/id-oficial.pdf'
          },
          {
            id: '2',
            producto: 'Wiki Gadget',
            categoria: 'Generales',
            documento: 'Ticket de compra del gadget',
            estado: 'Rechazado' as DocumentStatus,
            motivoRechazo: 'El ticket no es legible. Por favor, sube una imagen más clara.',
            fechaCarga: '2024-01-15'
          },
          {
            id: '3',
            producto: 'Wiki Gadget',
            categoria: 'Generales',
            documento: 'Comprobante de Domicilio',
            estado: 'Pendiente' as DocumentStatus,
            fechaCarga: '2024-01-16'
          },
          {
            id: '4',
            producto: 'Wiki Gadget',
            categoria: 'Generales',
            documento: 'Estado de cuenta',
            estado: 'Aprobado' as DocumentStatus,
            fechaCarga: '2024-01-14',
            url: '/documents/estado-cuenta.pdf'
          }
        ],
        coberturas: {}
      }
    };
    return mockDocuments;
  });

  const downloadDocument = useCallback(async (document: Document) => {
    if (!document.url) {
      setError('El documento no tiene una URL válida para descargar');
      return;
    }
    
    try {
      setLoading(true);
      setError(null);

      // En producción, aquí se haría la descarga real del documento desde la API
      // const response = await documentsService.downloadDocument(document.id);
      
      // Simulación de descarga
      const link = window.document.createElement('a');
      link.href = document.url;
      link.download = `${document.documento}-${document.producto}.pdf`;
      link.click();
      
    } catch (err) {
      console.error('Error al descargar documento:', err);
      setError('Error al descargar el documento. Inténtalo de nuevo.');
    } finally {
      setLoading(false);
    }
  }, []);

  const replaceDocument = useCallback(async (documentId: string, newFile: File) => {
    try {
      setLoading(true);
      setError(null);

      // En producción, aquí se subiría el archivo a la API
      // const response = await documentsService.uploadDocument(documentId, newFile);

      // Simulación de actualización
      setDocuments(prev => {
        const updated = { ...prev };
        
        Object.keys(updated).forEach(producto => {
          // Buscar en generales
          const generalIndex = updated[producto].generales.findIndex(doc => doc.id === documentId);
          if (generalIndex !== -1) {
            updated[producto].generales[generalIndex] = {
              ...updated[producto].generales[generalIndex],
              estado: 'Pendiente' as DocumentStatus,
              archivo: newFile,
              motivoRechazo: undefined,
              fechaCarga: new Date().toISOString().split('T')[0]
            };
          }

          // Buscar en coberturas
          Object.keys(updated[producto].coberturas).forEach(cobertura => {
            const coberturaIndex = updated[producto].coberturas[cobertura].findIndex(doc => doc.id === documentId);
            if (coberturaIndex !== -1) {
              updated[producto].coberturas[cobertura][coberturaIndex] = {
                ...updated[producto].coberturas[cobertura][coberturaIndex],
                estado: 'Pendiente' as DocumentStatus,
                archivo: newFile,
                motivoRechazo: undefined,
                fechaCarga: new Date().toISOString().split('T')[0]
              };
            }
          });
        });

        return updated;
      });

    } catch (err) {
      console.error('Error al reemplazar documento:', err);
      setError('Error al subir el documento. Inténtalo de nuevo.');
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteDocument = useCallback((documentId: string) => {
    setDocuments(prev => {
      const updated = { ...prev };
      
      Object.keys(updated).forEach(producto => {
        // Remover archivo de generales
        updated[producto].generales = updated[producto].generales.map(doc => 
          doc.id === documentId 
            ? { ...doc, archivo: undefined, url: undefined }
            : doc
        );

        // Remover archivo de coberturas
        Object.keys(updated[producto].coberturas).forEach(cobertura => {
          updated[producto].coberturas[cobertura] = updated[producto].coberturas[cobertura].map(doc =>
            doc.id === documentId
              ? { ...doc, archivo: undefined, url: undefined }
              : doc
          );
        });
      });

      return updated;
    });
  }, []);

  const getProgressStats = useCallback(() => {
    let total = 0;
    let approved = 0;
    let rejected = 0;
    let pending = 0;

    Object.values(documents).forEach(productDocs => {
      productDocs.generales.forEach(doc => {
        total++;
        switch (doc.estado) {
          case 'Aprobado': approved++; break;
          case 'Rechazado': rejected++; break;
          case 'Pendiente': pending++; break;
        }
      });

      Object.values(productDocs.coberturas).forEach(coverageDocs => {
        coverageDocs.forEach(doc => {
          total++;
          switch (doc.estado) {
            case 'Aprobado': approved++; break;
            case 'Rechazado': rejected++; break;
            case 'Pendiente': pending++; break;
          }
        });
      });
    });

    return { total, approved, rejected, pending };
  }, [documents]);

  return {
    documents,
    loading,
    error,
    downloadDocument,
    replaceDocument,
    deleteDocument,
    getProgressStats
  };
};
