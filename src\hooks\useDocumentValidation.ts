import { useState, useCallback, useMemo } from 'react';
import { Document, TimelineStep } from '@/types';
import { ReportsService } from '@/infrastructure/services/reportsService';

interface UseDocumentValidationReturn {
  documents: Document[];
  timelineSteps: TimelineStep[];
  loading: boolean;
  error: string | null;
  downloadDocument: (document: Document) => Promise<void>;
  uploadDocument: (numeroPoliza: string, documentId: string, file: File) => Promise<void>;
  fetchDocuments: (numeroPoliza: string) => Promise<void>;
  clearError: () => void;
  getProgressStats: () => {
    total: number;
    approved: number;
    rejected: number;
    pending: number;
  };
}

export const useDocumentValidation = (): UseDocumentValidationReturn => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [timelineSteps, setTimelineSteps] = useState<TimelineStep[]>([]);

  const reportsService = useMemo(() => new ReportsService(), []);

  /**
   * Obtiene los documentos de un reporte por número de póliza
   */
  const fetchDocuments = useCallback(async (numeroPoliza: string) => {
    try {
      setLoading(true);
      setError(null);

      const documentsData = await reportsService.getDocumentsByPoliza(numeroPoliza);
      setDocuments(documentsData);

      // Generar timeline basado en el primer documento (asumiendo que todos tienen el mismo status)
      const reportStatus = documentsData.length > 0 ? documentsData[0].estado : 'Documentos';
      const timeline = reportsService.generateTimelineSteps(reportStatus);
      setTimelineSteps(timeline);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError(errorMessage);
      console.error('Error fetching documents:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Descarga un documento específico
   */
  const downloadDocument = useCallback(async (document: Document) => {
    if (!document.url) {
      setError('El documento no tiene una URL válida para descargar');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Usar el servicio para obtener la URL de descarga
      const downloadUrl = await reportsService.downloadDocument(document.id);

      // Crear enlace de descarga
      const link = window.document.createElement('a');
      link.href = downloadUrl;
      link.download = `${document.documento}-${document.producto}.pdf`;
      link.target = '_blank';
      link.rel = 'noopener noreferrer';
      window.document.body.appendChild(link);
      link.click();
      window.document.body.removeChild(link);

    } catch (err) {
      console.error('Error al descargar documento:', err);
      setError('Error al descargar el documento. Inténtalo de nuevo.');
    } finally {
      setLoading(false);
    }
  }, [reportsService]);

  /**
   * Sube un documento para un reporte específico
   */
  const uploadDocument = useCallback(async (numeroPoliza: string, documentId: string, file: File) => {
    try {
      setLoading(true);
      setError(null);

      const updatedDocument = await reportsService.uploadDocument(numeroPoliza, documentId, file);

      // Actualizar el documento en la lista local
      setDocuments(prev =>
        prev.map(doc =>
          doc.id === documentId
            ? { ...doc, ...updatedDocument, estado: 'Pendiente' }
            : doc
        )
      );

    } catch (err) {
      console.error('Error al subir documento:', err);
      setError('Error al subir el documento. Inténtalo de nuevo.');
    } finally {
      setLoading(false);
    }
  }, [reportsService]);

  /**
   * Limpia los errores
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Obtiene estadísticas de progreso de los documentos
   */
  const getProgressStats = useCallback(() => {
    let total = 0;
    let approved = 0;
    let rejected = 0;
    let pending = 0;

    documents.forEach(doc => {
      total++;
      switch (doc.estado) {
        case 'Aprobado': approved++; break;
        case 'Rechazado': rejected++; break;
        case 'Pendiente': pending++; break;
      }
    });

    return { total, approved, rejected, pending };
  }, [documents]);

  return {
    documents,
    timelineSteps,
    loading,
    error,
    downloadDocument,
    uploadDocument,
    fetchDocuments,
    clearError,
    getProgressStats
  };
};
