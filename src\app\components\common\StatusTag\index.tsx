import React from 'react';
import * as S from './StatusTag.styles';
import { DocumentIcon, FiniquitoIcon, PagoIcon, FinalizadoIcon } from '../Icons';
import IconWrapper from '../IconWrapper';
import { ReportStatus } from '@/types';

interface StatusTagProps {
  status: ReportStatus;
  onClick?: () => void;
}

const getStatusIcon = (status: ReportStatus) => {
  switch (status) {
    case 'Documentos':
      return <DocumentIcon />;
    case 'Finiquito':
      return <FiniquitoIcon />;
    case 'Finalizado':
      return <FinalizadoIcon />;
    case 'Pago':
      return <PagoIcon />;
    default:
      return null;
  }
};

const StatusTag: React.FC<StatusTagProps> = ({ status, onClick }) => {
  return (
    <S.StatusContainer
      onClick={onClick}
      role="button"
      tabIndex={0}
      onKeyPress={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          onClick?.();
        }
      }}
    >
      <IconWrapper 
        icon={getStatusIcon(status)}
        onClick={onClick}
        title={`Estado: ${status}`}
      />
      <S.StatusText>{status}</S.StatusText>
    </S.StatusContainer>
  );
};

export default StatusTag;
