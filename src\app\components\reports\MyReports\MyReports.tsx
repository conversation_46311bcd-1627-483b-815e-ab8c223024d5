"use client";
import React, { useState, useCallback, useEffect } from "react";
import * as S from "./MyReports.styles";
import { StatusTag, FilterCheckbox, Table, StatusText } from "@/app/components/common";
import { Report, ReportStatus, StatusType } from "@/types";
import { useRouter } from "next/navigation";
import { mockReportsData } from "@/app/shared/mockData";
import { useReports } from "@/hooks/useReports";

const MyReports = () => {
  const router = useRouter();
  const [showIncomplete, setShowIncomplete] = useState(false);
  const [showFinished, setShowFinished] = useState(false);

  // Hook para manejar reportes desde la API
  const {
    reports,
    loading,
    error,
    pagination,
    fetchReports,
    updateReportStatus,
    clearError
  } = useReports();

  // Usar datos centralizados como fallback
  const allReports: Report[] = reports.length > 0 ? reports.map(report => ({
    poliza: report.numeroPoliza,
    numeroPoliza: report.numeroPoliza,
    estatusPoliza: 'Vigente' as StatusType, // Mapear según la lógica de negocio
    periodo: new Date(report.fechaCreacion).getFullYear().toString(),
    sumaAsegurada: report.cobertura,
    remanenteSuma: report.resultado,
    estatusReporte: report.status as ReportStatus
  })) : mockReportsData;

  // Cargar reportes al montar el componente
  useEffect(() => {
    fetchReports({
      page: 1,
      limit: 50,
      sortBy: 'createdAt',
      sortDir: 'desc'
    });
  }, [fetchReports]);

  const handleStatusClick = useCallback((numeroPoliza: string) => {
    router.push(`/accident-reports/${numeroPoliza}`);
  }, [router]);

  const handleNewReport = useCallback(() => {
    router.push("/siniestros/select");
  }, [router]);

  const filteredReports = allReports.filter(report => {
    if (!showIncomplete && !showFinished) return true;
    if (showIncomplete && ["Documentos", "Finiquito"].includes(report.estatusReporte)) return true;
    if (showFinished && ["Finalizado", "Pago"].includes(report.estatusReporte)) return true;
    return false;
  });

  const sortedReports = [...filteredReports].reverse();

  return (
    <S.Container>
      <S.TitleContainer>
        <S.Title>Mis reportes</S.Title>
        <S.HelpButton href="/faq" target="_blank" rel="noopener noreferrer">Ayuda</S.HelpButton>
      </S.TitleContainer>

      <S.FilterSection>
        <S.ReportButton onClick={handleNewReport}>
          Reportar un siniestro
        </S.ReportButton>
        <S.CheckboxContainer>
          <FilterCheckbox
            id="incomplete"
            label="Reportes incompletos"
            checked={showIncomplete}
            onChange={setShowIncomplete}
          />
          <FilterCheckbox
            id="finished"
            label="Reportes finalizados"
            checked={showFinished}
            onChange={setShowFinished}
          />
        </S.CheckboxContainer>
      </S.FilterSection>

      <Table
        data={sortedReports}
        columns={[
          { header: 'Póliza', accessor: 'poliza' },
          { header: 'Número de póliza', accessor: 'numeroPoliza' },
          {
            header: 'Estatus de la póliza',
            accessor: 'estatusPoliza',
            render: (value) => (
              <StatusText status={value as StatusType}>
                {value}
              </StatusText>
            )
          },
          { header: 'Periodo', accessor: 'periodo' },
          { header: 'Suma asegurada', accessor: 'sumaAsegurada' },
          { header: 'Remanente de suma asegurada', accessor: 'remanenteSuma' },
          {
            header: 'Estatus del reporte',
            accessor: 'estatusReporte',
            render: (value, item) => (
              <StatusTag
                status={value as ReportStatus}
                onClick={() => handleStatusClick(item.numeroPoliza)}
              />
            )
          }
        ]}
        emptyMessage="No se encontraron reportes disponibles."
        aria-label="Mis reportes"
      />
    </S.Container>
  );
};

export default MyReports;