import { Report, Document, DocumentStatus, TimelineStep } from "@/types";

// Archivo PDF de prueba común para todas las descargas
export const MOCK_PDF_URL = '/documents/documento-prueba.pdf';

// Datos mock centralizados para reportes y sus documentos
export const mockReportsData: Report[] = [
  {
    poliza: "Wiki Phishing",
    numeroPoliza: "WP-2024-001",
    estatusPoliza: "Vigente",
    periodo: "Del 24/10/2024 al 24/10/2025",
    sumaAsegurada: "$7,500.00",
    remanenteSuma: "$1,500.00",
    estatusReporte: "Documentos",
  },
  {
    poliza: "Wiki Rodada",
    numeroPoliza: "WR-2024-002",
    estatusPoliza: "Vencida",
    periodo: "Del 12/02/2024 al 12/02/2025",
    sumaAsegurada: "$15,500.00",
    remanenteSuma: "$15,500.00",
    estatusReporte: "Finiquito",
  },
  {
    poliza: "Wiki Gadget",
    numeroPoliza: "WG-2024-003",
    estatusPoliza: "Vigente",
    periodo: "Del 01/09/2024 al 01/09/2025",
    sumaAsegurada: "$25,000.00",
    remanenteSuma: "$25,000.00",
    estatusReporte: "Finalizado",
  },
  {
    poliza: "Wiki Asalto",
    numeroPoliza: "WA-2024-004",
    estatusPoliza: "Vencida",
    periodo: "Del 01/03/2024 al 01/03/2025",
    sumaAsegurada: "$35,500.00",
    remanenteSuma: "$15,500.00",
    estatusReporte: "Pago",
  },
];

// Documentos por número de póliza
export const mockDocumentsByPoliza: { [numeroPoliza: string]: Document[] } = {
  "WP-2024-001": [
    {
      id: '1',
      producto: 'Wiki Phishing',
      categoria: 'Ciberseguridad',
      documento: 'Identificación Oficial',
      estado: 'Aprobado' as DocumentStatus,
      fechaCarga: '2024-01-15',
      url: MOCK_PDF_URL
    },
    {
      id: '2',
      producto: 'Wiki Phishing',
      categoria: 'Ciberseguridad',
      documento: 'Evidencia del ataque',
      estado: 'Rechazado' as DocumentStatus,
      motivoRechazo: 'Las capturas de pantalla no son suficientemente claras. Necesitamos evidencia más detallada.',
      fechaCarga: '2024-01-15'
    },
    {
      id: '3',
      producto: 'Wiki Phishing',
      categoria: 'Ciberseguridad',
      documento: 'Comprobante de daños',
      estado: 'Pendiente' as DocumentStatus,
      fechaCarga: '2024-01-16'
    }
  ],
  "WR-2024-002": [
    {
      id: '4',
      producto: 'Wiki Rodada',
      categoria: 'Vehicular',
      documento: 'Licencia de conducir',
      estado: 'Aprobado' as DocumentStatus,
      fechaCarga: '2024-02-10',
      url: MOCK_PDF_URL
    },
    {
      id: '5',
      producto: 'Wiki Rodada',
      categoria: 'Vehicular',
      documento: 'Tarjeta de circulación',
      estado: 'Aprobado' as DocumentStatus,
      fechaCarga: '2024-02-10',
      url: MOCK_PDF_URL
    },
    {
      id: '6',
      producto: 'Wiki Rodada',
      categoria: 'Vehicular',
      documento: 'Reporte ministerial',
      estado: 'Pendiente' as DocumentStatus,
      fechaCarga: '2024-02-12'
    }
  ],
  "WG-2024-003": [
    {
      id: '7',
      producto: 'Wiki Gadget',
      categoria: 'Electrónicos',
      documento: 'Ticket de compra',
      estado: 'Aprobado' as DocumentStatus,
      fechaCarga: '2024-09-01',
      url: MOCK_PDF_URL
    },
    {
      id: '8',
      producto: 'Wiki Gadget',
      categoria: 'Electrónicos',
      documento: 'Garantía del producto',
      estado: 'Aprobado' as DocumentStatus,
      fechaCarga: '2024-09-01',
      url: MOCK_PDF_URL
    },
    {
      id: '9',
      producto: 'Wiki Gadget',
      categoria: 'Electrónicos',
      documento: 'Fotografías del daño',
      estado: 'Aprobado' as DocumentStatus,
      fechaCarga: '2024-09-02',
      url: MOCK_PDF_URL
    }
  ],
  "WA-2024-004": [
    {
      id: '10',
      producto: 'Wiki Asalto',
      categoria: 'Personal',
      documento: 'Denuncia ministerial',
      estado: 'Aprobado' as DocumentStatus,
      fechaCarga: '2024-03-01',
      url: MOCK_PDF_URL
    },
    {
      id: '11',
      producto: 'Wiki Asalto',
      categoria: 'Personal',
      documento: 'Identificación oficial',
      estado: 'Aprobado' as DocumentStatus,
      fechaCarga: '2024-03-01',
      url: MOCK_PDF_URL
    },
    {
      id: '12',
      producto: 'Wiki Asalto',
      categoria: 'Personal',
      documento: 'Lista de objetos robados',
      estado: 'Rechazado' as DocumentStatus,
      motivoRechazo: 'La lista no incluye valores estimados de los objetos. Por favor actualiza con precios.',
      fechaCarga: '2024-03-02'
    }
  ],
};

// Timeline basado en el estatus del reporte
export const getTimelineByReportStatus = (numeroPoliza: string): TimelineStep[] => {
  const report = mockReportsData.find(r => r.numeroPoliza === numeroPoliza);
  
  if (!report) {
    return [
      { id: '1', status: 'Documentos', label: 'Documentos', isActive: true, isCompleted: false },
      { id: '2', status: 'Finiquito', label: 'Propuesta de Finiquito', isActive: false, isCompleted: false },
      { id: '3', status: 'Pago', label: 'Procesando Pago', isActive: false, isCompleted: false },
      { id: '4', status: 'Finalizado', label: 'Fin de reclamación', isActive: false, isCompleted: false }
    ];
  }

  const status = report.estatusReporte;
  
  switch (status) {
    case 'Documentos':
      return [
        { id: '1', status: 'Documentos', label: 'Documentos', isActive: true, isCompleted: false },
        { id: '2', status: 'Finiquito', label: 'Propuesta de Finiquito', isActive: false, isCompleted: false },
        { id: '3', status: 'Pago', label: 'Procesando Pago', isActive: false, isCompleted: false },
        { id: '4', status: 'Finalizado', label: 'Fin de reclamación', isActive: false, isCompleted: false }
      ];
    case 'Finiquito':
      return [
        { id: '1', status: 'Documentos', label: 'Documentos', isActive: false, isCompleted: true },
        { id: '2', status: 'Finiquito', label: 'Propuesta de Finiquito', isActive: true, isCompleted: false },
        { id: '3', status: 'Pago', label: 'Procesando Pago', isActive: false, isCompleted: false },
        { id: '4', status: 'Finalizado', label: 'Fin de reclamación', isActive: false, isCompleted: false }
      ];
    case 'Pago':
      return [
        { id: '1', status: 'Documentos', label: 'Documentos', isActive: false, isCompleted: true },
        { id: '2', status: 'Finiquito', label: 'Propuesta de Finiquito', isActive: false, isCompleted: true },
        { id: '3', status: 'Pago', label: 'Procesando Pago', isActive: true, isCompleted: false },
        { id: '4', status: 'Finalizado', label: 'Fin de reclamación', isActive: false, isCompleted: false }
      ];
    case 'Finalizado':
      return [
        { id: '1', status: 'Documentos', label: 'Documentos', isActive: false, isCompleted: true },
        { id: '2', status: 'Finiquito', label: 'Propuesta de Finiquito', isActive: false, isCompleted: true },
        { id: '3', status: 'Pago', label: 'Procesando Pago', isActive: false, isCompleted: true },
        { id: '4', status: 'Finalizado', label: 'Fin de reclamación', isActive: true, isCompleted: false }
      ];
    default:
      return [
        { id: '1', status: 'Documentos', label: 'Documentos', isActive: true, isCompleted: false },
        { id: '2', status: 'Finiquito', label: 'Propuesta de Finiquito', isActive: false, isCompleted: false },
        { id: '3', status: 'Pago', label: 'Procesando Pago', isActive: false, isCompleted: false },
        { id: '4', status: 'Finalizado', label: 'Fin de reclamación', isActive: false, isCompleted: false }
      ];
  }
};

// Función para obtener documentos por número de póliza
export const getDocumentsByPoliza = (numeroPoliza: string): Document[] => {
  return mockDocumentsByPoliza[numeroPoliza] || [];
};

// Función para obtener un reporte por número de póliza
export const getReportByPoliza = (numeroPoliza: string): Report | undefined => {
  return mockReportsData.find(report => report.numeroPoliza === numeroPoliza);
};
