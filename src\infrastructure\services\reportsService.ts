/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from '@/core/axios';
import {
  ReportListParams,
  ReportListResponse,
  FiscalInfoRequest,
  AssociateDocsRequest,
  UpdateReportStatusRequest
} from '@/types/reports';
import { Document, TimelineStep, ReportStatus } from '@/types';

export class ReportsService {
  private baseUrl = '/v1/reports';

  /**
   * Obtiene la lista de reportes del usuario autenticado con filtros opcionales
   */
  async getReports(params?: ReportListParams): Promise<ReportListResponse> {
    try {
      const queryParams = new URLSearchParams();

      if (params?.status) queryParams.append('status', params.status);
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params?.sortDir) queryParams.append('sortDir', params.sortDir);

      const url = `${this.baseUrl}/me${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

      console.log('🔍 Fetching reports from:', url);
      const response = await axios.get<ReportListResponse>(url);

      console.log('✅ Reports response:', response.data);
      return response.data;
    } catch (error: unknown) {
      const axiosError = error as any;
      console.error('❌ Error al obtener reportes:', {
        message: axiosError.message,
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        url: axiosError.config?.url
      });

      // Proporcionar más información específica del error
      if (axiosError.response?.status === 404) {
        throw new Error('El endpoint de reportes no fue encontrado. Verifica que la API esté configurada correctamente.');
      } else if (axiosError.response?.status === 500) {
        throw new Error('Error interno del servidor. El endpoint de reportes está experimentando problemas.');
      } else if (axiosError.response?.status === 401) {
        throw new Error('No autorizado. Verifica que el usuario esté autenticado.');
      } else {
        throw new Error(`Error al cargar los reportes: ${axiosError.message || 'Error desconocido'}`);
      }
    }
  }

  /**
   * Guarda la información fiscal del usuario
   */
  async saveFiscalInfo(data: FiscalInfoRequest): Promise<{ message: string }> {
    try {
      const response = await axios.post(`${this.baseUrl}/info-fiscal`, data, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error al guardar información fiscal:', error);
      throw new Error('Error al guardar la información fiscal');
    }
  }

  /**
   * Asocia documentos fiscales precargados a una reclamación existente
   */
  async associateFiscalDocs(data: AssociateDocsRequest): Promise<{ message: string }> {
    try {
      const response = await axios.post(`${this.baseUrl}/info-fiscal/associate-docs`, data);
      
      return response.data;
    } catch (error) {
      console.error('Error al asociar documentos fiscales:', error);
      throw new Error('Error al asociar los documentos fiscales');
    }
  }

  /**
   * Actualiza el estado de una reclamación
   */
  async updateReportStatus(reportId: string, data: UpdateReportStatusRequest): Promise<{
    id: string;
    fechaCreacion: string;
    fechaIncidente: string;
    tipoIncidente: string;
    numeroPoliza: string;
    resultado: string;
    cobertura: string;
    status: string;
  }> {
    try {
      const response = await axios.patch(`${this.baseUrl}/${reportId}/status`, data);
      
      return response.data;
    } catch (error) {
      console.error('Error al actualizar estado del reporte:', error);
      throw new Error('Error al actualizar el estado del reporte');
    }
  }

  /**
   * Obtiene los detalles de un reporte específico
   */
  async getReportById(reportId: string): Promise<{
    id: string;
    fechaCreacion: string;
    fechaIncidente: string;
    tipoIncidente: string;
    numeroPoliza: string;
    resultado: string;
    cobertura: string;
    status: string;
  }> {
    try {
      const response = await axios.get(`${this.baseUrl}/${reportId}`);

      return response.data;
    } catch (error) {
      console.error('Error al obtener detalles del reporte:', error);
      throw new Error('Error al cargar los detalles del reporte');
    }
  }

  /**
   * Obtiene los documentos de un reporte específico por número de póliza
   */
  async getDocumentsByPoliza(numeroPoliza: string): Promise<Document[]> {
    try {
      const response = await axios.get(`${this.baseUrl}/documents/${numeroPoliza}`);

      return response.data;
    } catch (error) {
      console.error('Error al obtener documentos del reporte:', error);
      throw new Error('Error al cargar los documentos del reporte');
    }
  }

  /**
   * Sube un documento para un reporte específico
   */
  async uploadDocument(numeroPoliza: string, documentId: string, file: File): Promise<Document> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('documentId', documentId);

      const response = await axios.post(`${this.baseUrl}/documents/${numeroPoliza}/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error) {
      console.error('Error al subir documento:', error);
      throw new Error('Error al subir el documento');
    }
  }

  /**
   * Descarga un documento específico
   */
  async downloadDocument(documentId: string): Promise<string> {
    try {
      const response = await axios.get(`${this.baseUrl}/documents/${documentId}/download`);

      return response.data.url;
    } catch (error) {
      console.error('Error al descargar documento:', error);
      throw new Error('Error al descargar el documento');
    }
  }

  /**
   * Genera el timeline de pasos basado en el estado del reporte
   */
  generateTimelineSteps(status: string): TimelineStep[] {
    const allSteps: TimelineStep[] = [
      { id: '1', status: 'Documentos' as ReportStatus, label: 'Documentos', isActive: false, isCompleted: false },
      { id: '2', status: 'Finiquito' as ReportStatus, label: 'Propuesta de Finiquito', isActive: false, isCompleted: false },
      { id: '3', status: 'Pago' as ReportStatus, label: 'Procesando Pago', isActive: false, isCompleted: false },
      { id: '4', status: 'Finalizado' as ReportStatus, label: 'Fin de reclamación', isActive: false, isCompleted: false }
    ];

    const statusOrder = ['Documentos', 'Finiquito', 'Pago', 'Finalizado'];
    const currentIndex = statusOrder.indexOf(status);

    return allSteps.map((step, index) => ({
      ...step,
      isCompleted: index < currentIndex,
      isActive: index === currentIndex
    }));
  }
}
