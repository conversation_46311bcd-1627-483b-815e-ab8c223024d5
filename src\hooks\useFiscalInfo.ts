import { useState, useCallback } from 'react';
import { ReportsService } from '@/infrastructure/services/reportsService';
import { 
  FiscalInfoRequest, 
  AssociateDocsRequest 
} from '@/types/reports';

export const useFiscalInfo = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const reportsService = new ReportsService();

  /**
   * Guarda la información fiscal del usuario
   */
  const saveFiscalInfo = useCallback(async (data: FiscalInfoRequest) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(false);
      
      const response = await reportsService.saveFiscalInfo(data);
      
      setSuccess(true);
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError(errorMessage);
      console.error('Error saving fiscal info:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Asocia documentos fiscales precargados a una reclamación
   */
  const associateFiscalDocs = useCallback(async (data: AssociateDocsRequest) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(false);
      
      const response = await reportsService.associateFiscalDocs(data);
      
      setSuccess(true);
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError(errorMessage);
      console.error('Error associating fiscal docs:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Resetea el estado del hook
   */
  const reset = useCallback(() => {
    setError(null);
    setSuccess(false);
    setLoading(false);
  }, []);

  /**
   * Limpia solo los errores
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Limpia solo el estado de éxito
   */
  const clearSuccess = useCallback(() => {
    setSuccess(false);
  }, []);

  return {
    loading,
    error,
    success,
    saveFiscalInfo,
    associateFiscalDocs,
    reset,
    clearError,
    clearSuccess,
  };
};
