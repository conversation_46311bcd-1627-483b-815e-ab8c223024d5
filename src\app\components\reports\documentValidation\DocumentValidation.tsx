"use client";
import React, { useState, useCallback, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import * as S from "./DocumentValidation.styles";
import {
  Document,
  TimelineStep,
  Report,
} from "@/types";
import {
  Close,
} from "@mui/icons-material";
import FileUploader from "@/app/components/common/FileUploader";
import { TimelineSteps } from "@/app/components/common";
import DocumentsTable from "./DocumentsTable";
import { ReportStatusHeader } from "@/app/components/reports";
import { useDocumentValidation } from "@/hooks/useDocumentValidation";
import { useReports } from "@/hooks/useReports";

const DocumentValidation = () => {
  const router = useRouter();
  const params = useParams();

  // Obtener el número de póliza de la URL
  const numeroPoliza = (params?.numeroPoliza as string) || "WP-2024-001";

  // Estados para modales
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadingDocument, setUploadingDocument] = useState<Document | null>(
    null
  );
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // Estados para datos del reporte
  const [currentReport, setCurrentReport] = useState<Report | null>(null);

  // Hooks para manejar documentos y reportes
  const {
    documents,
    timelineSteps,
    loading: documentsLoading,
    error: documentsError,
    downloadDocument: downloadDocumentFromService,
    uploadDocument,
    fetchDocuments,
    clearError
  } = useDocumentValidation();

  const { fetchReportById } = useReports();

  // Cargar datos cuando cambie el número de póliza
  useEffect(() => {
    const loadReportData = async () => {
      try {
        // Cargar datos del reporte
        const report = await fetchReportById(numeroPoliza);
        setCurrentReport(report as Report);

        // Cargar documentos del reporte
        await fetchDocuments(numeroPoliza);
      } catch (error) {
        console.error('Error loading report data:', error);
      }
    };

    loadReportData();
  }, [numeroPoliza, fetchReportById, fetchDocuments]);

  const handleBackToReports = useCallback(() => {
    router.push("/accident-reports");
  }, [router]);

  const handleDownloadDocument = useCallback(async (document: Document) => {
    try {
      await downloadDocumentFromService(document);
    } catch (error) {
      console.error("Error al descargar documento:", error);
    }
  }, [downloadDocumentFromService]);

  const handleShowUploadModal = useCallback((document: Document) => {
    setUploadingDocument(document);
    setSelectedFile(null); // Limpiar archivo anterior
    setShowUploadModal(true);
  }, []);

  const handleFileUpload = useCallback(
    async (file: File | null) => {
      if (!file || !uploadingDocument) {
        setSelectedFile(null);
        return;
      }

      try {
        // Actualizar el estado del archivo seleccionado
        setSelectedFile(file);

        // Subir el archivo usando el servicio
        await uploadDocument(numeroPoliza, uploadingDocument.id, file);

        // Cerrar modal
        setShowUploadModal(false);
        setUploadingDocument(null);
        setSelectedFile(null);
      } catch (error) {
        console.error("Error al subir archivo:", error);
        // El error ya se maneja en el hook
      }
    },
    [uploadingDocument, numeroPoliza, uploadDocument]
  );

  const handleTimelineStepClick = useCallback((step: TimelineStep) => {
    console.log("Timeline step clicked:", step);
    // Aquí puedes manejar la navegación o acciones específicas del timeline
  }, []);

  // Mostrar loading state
  if (documentsLoading) {
    return (
      <S.Container>
        <div style={{ textAlign: 'center', padding: '2rem' }}>
          Cargando documentos...
        </div>
      </S.Container>
    );
  }

  // Mostrar error state
  if (documentsError) {
    return (
      <S.Container>
        <div style={{ textAlign: 'center', padding: '2rem', color: 'red' }}>
          Error al cargar documentos: {documentsError}
          <button onClick={clearError} style={{ marginLeft: '1rem' }}>
            Reintentar
          </button>
        </div>
      </S.Container>
    );
  }

  return (
    <S.Container>
      <S.HeaderSection>
        <ReportStatusHeader
          status={currentReport?.estatusReporte || "Documentos"}
          onBackClick={handleBackToReports}
        />

        {/* Timeline de progreso usando TimelineSteps */}
        <S.TimelineContainer>
          <TimelineSteps
            steps={timelineSteps}
            onStepClick={handleTimelineStepClick}
          />
        </S.TimelineContainer>
      </S.HeaderSection>

      <S.DocumentsSection>
        <S.SectionHeader>
          <S.SectionTitle>Documentos requeridos</S.SectionTitle>
          <S.SectionSubtitle>
            Revisa el estado de tus documentos y sube los que falten o necesiten
            corrección
          </S.SectionSubtitle>
        </S.SectionHeader>

        <DocumentsTable
          documents={documents}
          onDownloadDocument={handleDownloadDocument}
          onShowUploadModal={handleShowUploadModal}
          emptyMessage="No hay documentos disponibles"
        />
      </S.DocumentsSection>

      {/* Modal para subir archivo */}
      {showUploadModal && uploadingDocument && (
        <S.ModalOverlay onClick={() => setShowUploadModal(false)}>
          <S.Modal onClick={(e) => e.stopPropagation()}>
            <S.ModalHeader>
              <S.ModalTitle>Subir nuevo documento</S.ModalTitle>
              <S.CloseButton onClick={() => setShowUploadModal(false)}>
                <Close />
              </S.CloseButton>
            </S.ModalHeader>

            <S.ModalContent>
              <p style={{ marginBottom: "16px", color: "#6b7280" }}>
                Documento: <strong>{uploadingDocument.documento}</strong>
              </p>

              <FileUploader 
                onFileSelect={handleFileUpload} 
                currentFile={selectedFile ?? undefined}
              />
            </S.ModalContent>
          </S.Modal>
        </S.ModalOverlay>
      )}
    </S.Container>
  );
};

export default DocumentValidation;
