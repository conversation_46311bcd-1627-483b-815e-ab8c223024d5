"use client";
import React, { useState, useCallback, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import * as S from "./DocumentValidation.styles";
import {
  Document,
  TimelineStep,
  Report,
} from "@/types";
import {
  Close,
} from "@mui/icons-material";
import FileUploader from "@/app/components/common/FileUploader";
import { TimelineSteps } from "@/app/components/common";
import DocumentsTable from "./DocumentsTable";
import {
  getDocumentsByPoliza,
  getTimelineByReportStatus,
  getReportByPoliza,
} from "@/app/shared/mockData";
import { ReportStatusHeader } from "@/app/components/reports";

const DocumentValidation = () => {
  const router = useRouter();
  const params = useParams();

  // Obtener el número de póliza de la URL
  const numeroPoliza = (params?.numeroPoliza as string) || "WP-2024-001";

  // Estados para modales
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadingDocument, setUploadingDocument] = useState<Document | null>(
    null
  );
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // Estados para datos del reporte
  const [currentReport, setCurrentReport] = useState<Report | null>(null);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [timelineSteps, setTimelineSteps] = useState<TimelineStep[]>([]);

  // Cargar datos cuando cambie el número de póliza
  useEffect(() => {
    const report = getReportByPoliza(numeroPoliza);
    const reportDocuments = getDocumentsByPoliza(numeroPoliza);
    const timeline = getTimelineByReportStatus(numeroPoliza);

    setCurrentReport(report || null);
    setDocuments(reportDocuments);
    setTimelineSteps(timeline);
  }, [numeroPoliza]);

  const handleBackToReports = useCallback(() => {
    router.push("/accident-reports");
  }, [router]);

  const handleDownloadDocument = useCallback(async (document: Document) => {
    if (!document.url) return;

    try {
      console.log("Descargando documento:", document);

      // Simulación de descarga
      const link = window.document.createElement("a");
      link.href = document.url;
      link.download = `${document.documento}-${document.producto}.pdf`;
      link.click();
    } catch (error) {
      console.error("Error al descargar documento:", error);
    }
  }, []);

  const handleShowUploadModal = useCallback((document: Document) => {
    setUploadingDocument(document);
    setSelectedFile(null); // Limpiar archivo anterior
    setShowUploadModal(true);
  }, []);

  const handleFileUpload = useCallback(
    (file: File | null) => {
      if (!file || !uploadingDocument) {
        setSelectedFile(null);
        return;
      }

      // Actualizar el estado del archivo seleccionado
      setSelectedFile(file);
      
      // Aquí se haría la subida real del archivo
      console.log(
        "Subiendo archivo:",
        file,
        "para documento:",
        uploadingDocument
      );

      // Actualizar el documento en la lista con el nuevo archivo
      setDocuments(prevDocs => 
        prevDocs.map(doc => 
          doc.id === uploadingDocument.id 
            ? { ...doc, archivo: file, estado: 'Pendiente' as const }
            : doc
        )
      );

      // Cerrar modal
      setShowUploadModal(false);
      setUploadingDocument(null);
      setSelectedFile(null);
    },
    [uploadingDocument]
  );

  const handleTimelineStepClick = useCallback((step: TimelineStep) => {
    console.log("Timeline step clicked:", step);
    // Aquí puedes manejar la navegación o acciones específicas del timeline
  }, []);

  return (
    <S.Container>
      <S.HeaderSection>
        <ReportStatusHeader
          status={currentReport?.estatusReporte || "Documentos"}
          onBackClick={handleBackToReports}
        />

        {/* Timeline de progreso usando TimelineSteps */}
        <S.TimelineContainer>
          <TimelineSteps
            steps={timelineSteps}
            onStepClick={handleTimelineStepClick}
          />
        </S.TimelineContainer>
      </S.HeaderSection>

      <S.DocumentsSection>
        <S.SectionHeader>
          <S.SectionTitle>Documentos requeridos</S.SectionTitle>
          <S.SectionSubtitle>
            Revisa el estado de tus documentos y sube los que falten o necesiten
            corrección
          </S.SectionSubtitle>
        </S.SectionHeader>

        <DocumentsTable
          documents={documents}
          onDownloadDocument={handleDownloadDocument}
          onShowUploadModal={handleShowUploadModal}
          emptyMessage="No hay documentos disponibles"
        />
      </S.DocumentsSection>

      {/* Modal para subir archivo */}
      {showUploadModal && uploadingDocument && (
        <S.ModalOverlay onClick={() => setShowUploadModal(false)}>
          <S.Modal onClick={(e) => e.stopPropagation()}>
            <S.ModalHeader>
              <S.ModalTitle>Subir nuevo documento</S.ModalTitle>
              <S.CloseButton onClick={() => setShowUploadModal(false)}>
                <Close />
              </S.CloseButton>
            </S.ModalHeader>

            <S.ModalContent>
              <p style={{ marginBottom: "16px", color: "#6b7280" }}>
                Documento: <strong>{uploadingDocument.documento}</strong>
              </p>

              <FileUploader 
                onFileSelect={handleFileUpload} 
                currentFile={selectedFile ?? undefined}
              />
            </S.ModalContent>
          </S.Modal>
        </S.ModalOverlay>
      )}
    </S.Container>
  );
};

export default DocumentValidation;
