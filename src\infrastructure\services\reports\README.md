# Reports Integration

Esta documentación describe la integración de los endpoints de reports en el proyecto.

## Estructura de Archivos

```
src/
├── infrastructure/services/
│   └── reportsService.ts          # Servicio principal para endpoints de reports
├── hooks/
│   ├── useReports.ts              # Hook para manejar lista de reportes
│   └── useFiscalInfo.ts           # Hook para información fiscal
├── types/
│   └── reports.ts                 # Tipos TypeScript actualizados
└── app/components/reports/
    ├── FiscalInfo/
    │   ├── FiscalInfoForm.tsx     # Formulario de información fiscal
    │   └── AssociateFiscalDocs.tsx # Componente para asociar documentos
    └── ReportsExample.tsx         # Ejemplo de uso completo
```

## Endpoints Integrados

### 1. GET /v1/reports/me
- **Descripción**: Obtiene reportes del usuario autenticado
- **Parámetros**: status, page, limit, sortBy, sortDir
- **Hook**: `useReports.fetchReports()`

### 2. POST /v1/reports/info-fiscal
- **Descripción**: Guarda información fiscal del usuario
- **Tipo**: `FiscalInfoRequest`
- **Hook**: `useFiscalInfo.saveFiscalInfo()`

### 3. POST /v1/reports/info-fiscal/associate-docs
- **Descripción**: Asocia documentos fiscales a una reclamación
- **Tipo**: `AssociateDocsRequest`
- **Hook**: `useFiscalInfo.associateFiscalDocs()`

### 4. PATCH /v1/reports/{id}/status
- **Descripción**: Actualiza el estado de un reporte
- **Tipo**: `UpdateReportStatusRequest`
- **Hook**: `useReports.updateReportStatus()`

## Uso de Hooks

### useReports

```typescript
import { useReports } from '@/hooks/useReports';

const MyComponent = () => {
  const { 
    reports, 
    loading, 
    error, 
    pagination, 
    fetchReports, 
    updateReportStatus 
  } = useReports();

  useEffect(() => {
    fetchReports({
      page: 1,
      limit: 10,
      status: 'active',
      sortBy: 'createdAt',
      sortDir: 'desc'
    });
  }, [fetchReports]);

  const handleStatusUpdate = async (reportId: string, newStatus: string) => {
    try {
      await updateReportStatus(reportId, newStatus);
      // El estado se actualiza automáticamente
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  if (loading) return <div>Cargando...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      {reports.map(report => (
        <div key={report.id}>
          <h3>{report.numeroPoliza}</h3>
          <p>Status: {report.status}</p>
          <button onClick={() => handleStatusUpdate(report.id, 'completed')}>
            Marcar como completado
          </button>
        </div>
      ))}
    </div>
  );
};
```

### useFiscalInfo

```typescript
import { useFiscalInfo } from '@/hooks/useFiscalInfo';

const FiscalComponent = () => {
  const { loading, error, success, saveFiscalInfo, associateFiscalDocs } = useFiscalInfo();

  const handleSaveFiscalInfo = async (data: FiscalInfoRequest) => {
    try {
      await saveFiscalInfo(data);
      console.log('Información guardada exitosamente');
    } catch (error) {
      console.error('Error:', error);
    }
  };

  const handleAssociateDocs = async (reclamacionId: string) => {
    try {
      await associateFiscalDocs({ reclamacionId });
      console.log('Documentos asociados exitosamente');
    } catch (error) {
      console.error('Error:', error);
    }
  };

  return (
    <div>
      {loading && <div>Procesando...</div>}
      {error && <div>Error: {error}</div>}
      {success && <div>¡Operación exitosa!</div>}
      {/* Formulario aquí */}
    </div>
  );
};
```

## Componentes Disponibles

### FiscalInfoForm
Formulario completo para capturar información fiscal del usuario.

```typescript
import { FiscalInfoForm } from '@/app/components/reports';

<FiscalInfoForm
  onSuccess={() => console.log('Guardado exitosamente')}
  onCancel={() => console.log('Cancelado')}
/>
```

### AssociateFiscalDocs
Componente para asociar documentos fiscales a una reclamación.

```typescript
import { AssociateFiscalDocs } from '@/app/components/reports';

<AssociateFiscalDocs
  onSuccess={() => console.log('Documentos asociados')}
  onCancel={() => console.log('Cancelado')}
/>
```

## Tipos TypeScript

### FiscalInfoRequest
```typescript
interface FiscalInfoRequest {
  ocupacionProfesionActividad: string;
  nombreEmpresaLaboral: string;
  numeroSerieCertificadoDigital: string;
  curp: string;
  rfc: string;
  claveCuentaBancaria: string;
  funcionarioGobiernoAltaJerarquia: boolean;
  conyugeDependienteEconomicoFuncionario: boolean;
  negocioPropioaccionistaSociedad: boolean;
  actuaNombreCuentaPropia: boolean;
  comprobanteDomicilio: string;
  constanciaCurp: string;
  constanciaSituacionFiscal: string;
  caratulaBancaria: string;
  identificacionOficial: string;
}
```

### ReportListParams
```typescript
interface ReportListParams {
  status?: string;
  page?: number;
  limit?: number;
  sortBy?: 'createdAt' | 'updatedAt';
  sortDir?: 'asc' | 'desc';
}
```

## Manejo de Errores

Todos los hooks incluyen manejo de errores integrado:

- Los errores se capturan automáticamente
- Se almacenan en el estado `error` del hook
- Se pueden limpiar usando `clearError()`
- Los errores se logean en la consola para debugging

## Integración con Componentes Existentes

El componente `MyReports` ha sido actualizado para usar el nuevo hook `useReports`, manteniendo compatibilidad con los datos mock existentes como fallback.

## Próximos Pasos

1. Implementar validación de formularios más robusta
2. Agregar soporte para subida de archivos en los campos de documentos
3. Implementar paginación en la interfaz de usuario
4. Agregar filtros avanzados para la lista de reportes
5. Implementar notificaciones toast para feedback del usuario
