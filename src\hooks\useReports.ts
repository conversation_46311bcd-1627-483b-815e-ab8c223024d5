import { useState, useCallback } from 'react';
import { ReportsService } from '@/infrastructure/services/reportsService';
import { 
  ReportListParams, 
  ReportListResponse, 
  ReportItem 
} from '@/types/reports';

export const useReports = () => {
  const [reports, setReports] = useState<ReportItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0,
  });

  const reportsService = new ReportsService();

  /**
   * Obtiene la lista de reportes con parámetros opcionales
   */
  const fetchReports = useCallback(async (params?: ReportListParams) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await reportsService.getReports(params);
      
      setReports(response.data);
      setPagination(response.meta);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError(errorMessage);
      console.error('Error fetching reports:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Obtiene los detalles de un reporte específico
   */
  const fetchReportById = useCallback(async (reportId: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const report = await reportsService.getReportById(reportId);
      
      return report;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError(errorMessage);
      console.error('Error fetching report details:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Actualiza el estado de un reporte
   */
  const updateReportStatus = useCallback(async (reportId: string, status: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const updatedReport = await reportsService.updateReportStatus(reportId, { status });
      
      // Actualizar el reporte en la lista local
      setReports(prevReports => 
        prevReports.map(report => 
          report.id === reportId 
            ? { ...report, status: updatedReport.status }
            : report
        )
      );
      
      return updatedReport;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError(errorMessage);
      console.error('Error updating report status:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Refresca la lista de reportes
   */
  const refreshReports = useCallback((params?: ReportListParams) => {
    return fetchReports(params);
  }, [fetchReports]);

  /**
   * Limpia los errores
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    reports,
    loading,
    error,
    pagination,
    fetchReports,
    fetchReportById,
    updateReportStatus,
    refreshReports,
    clearError,
  };
};
