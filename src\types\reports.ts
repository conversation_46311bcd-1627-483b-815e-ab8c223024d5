export type ReportStatus = 'Documentos' | 'Finiquito' | 'Finalizado' | 'Pago';

export type StatusType = 'Vigente' | 'Vencida' | 'Aprobado' | 'Pendiente' | 'Rechazado' ;

export interface Report {
  poliza: string;
  numeroPoliza: string;
  estatusPoliza: StatusType;
  periodo: string;
  sumaAsegurada: string;
  remanenteSuma: string;
  estatusReporte: ReportStatus;
}

export interface TimelineStep {
  id: string;
  status: ReportStatus;
  label: string;
  isActive: boolean;
  isCompleted: boolean;
}
